{"editor.copyWithSyntaxHighlighting": false, "diffEditor.ignoreTrimWhitespace": false, "editor.emptySelectionClipboard": false, "workbench.editor.enablePreview": false, "window.newWindowDimensions": "inherit", "files.trimTrailingWhitespace": true, "diffEditor.renderSideBySide": false, "editor.snippetSuggestions": "top", "editor.detectIndentation": false, "window.nativeFullScreen": true, "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "editor.minimap.enabled": false, "editor.lineNumbers": "off", "editor.guides.indentation": false, "breadcrumbs.enabled": false, "scm.diffDecorations": "none", "editor.hover.delay": 1500, "editor.hover.enabled": true, "editor.matchBrackets": "never", "workbench.tips.enabled": false, "editor.colorDecorators": false, "git.decorations.enabled": true, "workbench.startupEditor": "none", "editor.lightbulb.enabled": "off", "editor.selectionHighlight": false, "editor.overviewRulerBorder": false, "editor.renderLineHighlight": "none", "editor.occurrencesHighlight": "off", "problems.decorations.enabled": false, "editor.renderControlCharacters": false, "editor.hideCursorInOverviewRuler": true, "terminal.integrated.fontSize": 14, "search.useIgnoreFiles": false, "search.exclude": {"**/vendor/{[^l],?[^ai]}*": true, "**/public/{[^i],?[^n]}*": true, "**/node_modules": true, "**/dist": true, "**/_ide_helper.php": true, "**/composer.lock": true, "**/package-lock.json": true, "storage": true, ".phpunit.result.cache": true}, "editor.wordSeparators": "`~!@#%^&*()=+[{]}\\|;:'\",.<>/?", "emmet.includeLanguages": {"blade": "html", "vue-html": "html", "vue": "html", "react": "html", "javascript": "html"}, "files.associations": {".php_cs": "php", ".php_cs.dist": "php", ".tf": "terraform"}, "php.suggest.basic": false, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[tailwindcss]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "prettier.requireConfig": true, "prettier.useEditorConfig": false, "explorer.sortOrder": "type", "prettier.tabWidth": 4, "vetur.format.options.tabSize": 4, "workbench.tree.indent": 15, "[html]": {"editor.formatOnSave": true}, "editor.wordWrapColumn": 120, "files.autoSave": "after<PERSON>elay", "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "editor.quickSuggestions": {"strings": true}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "security.workspace.trust.untrustedFiles": "open", "editor.linkedEditing": true, "editor.formatOnSave": false, "editor.fontLigatures": "'ss01', 'ss02', 'ss03', 'ss04', 'ss05', 'ss06', 'ss07', 'ss08', 'calt', 'dlig'", "diffEditor.wordWrap": "on", "notebook.output.wordWrap": true, "editor.fontSize": 14, "editor.minimap.maxColumn": 250, "codesnap.containerPadding": "8em", "codesnap.boxShadow": "rgba(0, 0, 0, 0.55) 0px 12px 24px", "explorer.confirmDelete": false, "explorer.compactFolders": false, "editor.accessibilitySupport": "off", "chat.editor.wordWrap": "on", "editor.wordWrap": "wordWrapColumn", "[php]": {"editor.defaultFormatter": "bmewburn.vscode-intelephense-client"}, "tailwindCSS.includeLanguages": {"plaintext": "html"}, "tailwindCSS.experimental.configFile": null, "editor.fontWeight": "400", "workbench.activityBar.location": "hidden", "workbench.statusBar.visible": false, "editor.inlineSuggest.suppressSuggestions": true, "codesnap.backgroundColor": "#FFC540", "codesnap.showLineNumbers": false, "codesnap.roundedCorners": true, "editor.padding.top": 16, "editor.cursorBlinking": "solid", "editor.stickyScroll.enabled": false, "material-icon-theme.saturation": 0, "vscode_custom_css.imports": ["file:///Users/<USER>/custom-vscode.css", "file:///Users/<USER>/vscode-script.js"], "material-icon-theme.files.color": "#42a5f5", "workbench.tree.enableStickyScroll": false, "workbench.colorCustomizations": {"terminal.background": "#0a0a0a", "terminal.foreground": "#e0e0e0", "terminalCursor.background": "#e0e0e0", "terminalCursor.foreground": "#0a0a0a", "terminal.ansiBlack": "#2d2d2d", "terminal.ansiRed": "#f92672", "terminal.ansiGreen": "#a6e22e", "terminal.ansiYellow": "#f4bf75", "terminal.ansiBlue": "#66d9ef", "terminal.ansiMagenta": "#ae81ff", "terminal.ansiCyan": "#a1efe4", "terminal.ansiWhite": "#f8f8f2", "terminal.ansiBrightBlack": "#75715e", "terminal.ansiBrightRed": "#f92672", "terminal.ansiBrightGreen": "#a6e22e", "terminal.ansiBrightYellow": "#f4bf75", "terminal.ansiBrightBlue": "#66d9ef", "terminal.ansiBrightMagenta": "#ae81ff", "terminal.ansiBrightCyan": "#a1efe4", "terminal.ansiBrightWhite": "#f9f8f5"}, "workbench.settings.applyToAllProfiles": ["workbench.colorCustomizations"], "database-client.autoSync": true, "workbench.colorTheme": "One Monokai 80s Original", "augment.advanced": {}, "augment.nextEdit.showDiffInHover": true, "augment.chat.userGuidelines": "Be quick and brief with your responses. Edit the files in scope to my issue. Feel free to look at any files or search for them in case you need more context.", "augment.nextEdit.enableGlobalBackgroundSuggestions": true, "augment.nextEdit.highlightSuggestionsInTheEditor": true, "terminal.integrated.cursorBlinking": true, "terminal.integrated.cursorStyle": "block"}