#!/usr/bin/env python3
"""
Short Squeeze Scanner - Autotrader App
Scans for potential short squeeze candidates under $20 with high short interest
"""

import yfinance as yf
import pandas as pd
from tabulate import tabulate
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
import time
import sys

console = Console()

# Example stock list (you can expand this or automate it later)
DEFAULT_TICKERS = ['BEEM', 'FSR', 'EVGO', 'PTEN', 'BBBY', 'RIOT', 'AMC', 'GME',
                   'SPRT', 'ATER', 'PROG', 'BBIG', 'IRNT', 'OPAD', 'GREE', 'PHUN']

def fetch_stock_data(ticker):
    """Fetch stock data for a given ticker"""
    try:
        stock = yf.Ticker(ticker)

        # Try to get basic info first
        try:
            info = stock.info
        except:
            info = {}

        # Try to get historical data
        try:
            hist = stock.history(period="1d")
            if not hist.empty:
                current_price = hist['Close'].iloc[-1]
            else:
                # Fallback to info price if available
                current_price = info.get('regularMarketPrice', info.get('currentPrice', 0))
        except:
            current_price = info.get('regularMarketPrice', info.get('currentPrice', 0))

        if current_price == 0:
            return None

        return {
            'Ticker': ticker,
            'Price': round(float(current_price), 2),
            'Short %': (info.get('shortPercentOfFloat', 0) * 100) if info.get('shortPercentOfFloat') else 0,
            'Float': info.get('floatShares', info.get('sharesOutstanding', 0)),
            'Market Cap': info.get('marketCap', 0),
            'Volume': info.get('regularMarketVolume', 0),
            'Avg Volume': info.get('averageVolume', 0),
            'Beta': info.get('beta', 0)
        }
    except Exception as e:
        console.print(f"[red]Error fetching data for {ticker}: {str(e)[:100]}...[/red]")
        return None

def get_demo_data():
    """Return demo data when API is not available"""
    return [
        {'Ticker': 'DEMO1', 'Price': 15.50, 'Short %': 25.3, 'Float': 50000000, 'Market Cap': 775000000, 'Volume': 2500000, 'Avg Volume': 1800000, 'Beta': 1.2},
        {'Ticker': 'DEMO2', 'Price': 8.75, 'Short %': 18.7, 'Float': 30000000, 'Market Cap': 262500000, 'Volume': 1200000, 'Avg Volume': 900000, 'Beta': 1.5},
        {'Ticker': 'DEMO3', 'Price': 19.99, 'Short %': 22.1, 'Float': 75000000, 'Market Cap': 1499250000, 'Volume': 3200000, 'Avg Volume': 2100000, 'Beta': 0.8},
    ]

def format_number(num):
    """Format large numbers for better readability"""
    if num >= 1e9:
        return f"{num/1e9:.1f}B"
    elif num >= 1e6:
        return f"{num/1e6:.1f}M"
    elif num >= 1e3:
        return f"{num/1e3:.1f}K"
    else:
        return str(num)

def scan_for_squeeze_candidates(tickers=None, max_price=20, min_short_percent=15, use_demo=False):
    """Scan for potential short squeeze candidates"""
    if tickers is None:
        tickers = DEFAULT_TICKERS

    console.print("🚨 [bold red]SCANNING FOR SHORT-SQUEEZE CANDIDATES[/bold red] 🚨\n")
    console.print(f"Criteria: Price ≤ ${max_price}, Short Interest ≥ {min_short_percent}%\n")

    results = []

    if use_demo:
        console.print("[yellow]Using demo data (API unavailable)[/yellow]\n")
        results = get_demo_data()
    else:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Fetching stock data...", total=len(tickers))

            for ticker in tickers:
                progress.update(task, description=f"Fetching data for {ticker}...")
                data = fetch_stock_data(ticker)
                if data:
                    results.append(data)
                progress.advance(task)
                time.sleep(0.1)  # Be nice to the API

        # If no results from API, fall back to demo data
        if not results:
            console.print("[yellow]No data retrieved from API. Using demo data instead.[/yellow]\n")
            results = get_demo_data()

    # Filter results based on criteria
    filtered_results = [
        r for r in results
        if r and r['Price'] <= max_price and r['Short %'] >= min_short_percent
    ]

    if not filtered_results:
        console.print("[yellow]No stocks found matching the criteria.[/yellow]")
        console.print("[cyan]Try adjusting the criteria (lower short % or higher max price)[/cyan]")
        return

    # Sort by short percentage (highest first)
    filtered_results.sort(key=lambda x: x['Short %'], reverse=True)

    # Create rich table for better display
    table = Table(title="🎯 Short Squeeze Candidates")
    table.add_column("Ticker", style="cyan", no_wrap=True)
    table.add_column("Price", style="green")
    table.add_column("Short %", style="red")
    table.add_column("Float", style="blue")
    table.add_column("Market Cap", style="magenta")
    table.add_column("Volume", style="yellow")
    table.add_column("Avg Volume", style="yellow")
    table.add_column("Beta", style="white")

    for stock in filtered_results:
        table.add_row(
            stock['Ticker'],
            f"${stock['Price']:.2f}",
            f"{stock['Short %']:.1f}%",
            format_number(stock['Float']),
            format_number(stock['Market Cap']),
            format_number(stock['Volume']),
            format_number(stock['Avg Volume']),
            f"{stock['Beta']:.2f}" if stock['Beta'] else "N/A"
        )

    console.print(table)

    # Also print as simple table for copy/paste
    console.print("\n📋 [bold]Copy-friendly format:[/bold]")
    print(tabulate(filtered_results, headers="keys", tablefmt="fancy_grid"))

    return filtered_results

def main():
    """Main function"""
    console.print("[bold blue]🤖 AutoTrader - Short Squeeze Scanner[/bold blue]")
    console.print("=" * 50)

    # Check if user wants demo mode
    use_demo = len(sys.argv) > 1 and sys.argv[1].lower() == 'demo'

    try:
        # You can customize these parameters
        results = scan_for_squeeze_candidates(
            tickers=DEFAULT_TICKERS,
            max_price=20,
            min_short_percent=15,
            use_demo=use_demo
        )

        if results:
            console.print(f"\n✅ Found {len(results)} potential candidates!")
            console.print("\n⚠️  [yellow]Disclaimer: This is for educational purposes only. "
                         "Always do your own research before making investment decisions.[/yellow]")

            if use_demo:
                console.print("\n[cyan]💡 Tip: Run without 'demo' argument to fetch real data[/cyan]")

    except KeyboardInterrupt:
        console.print("\n[red]Scan interrupted by user.[/red]")
    except Exception as e:
        console.print(f"\n[red]An error occurred: {str(e)}[/red]")
        console.print("\n[cyan]💡 Try running with 'demo' argument: python squeeze_scanner.py demo[/cyan]")

if __name__ == "__main__":
    main()
