# squeeze_scanner_v2.py

import os
import requests
import pandas as pd
from datetime import datetime
from tabulate import tabulate
from smtplib import SMTP_SSL
from email.message import EmailMessage
import praw
from collections import Counter

# CONFIG
API_KEY = os.getenv("FINNHUB_API_KEY")  # Set this in your shell or .env
ALERT_EMAIL = os.getenv("ALERT_EMAIL")   # Your email to receive alerts
EMAIL_PASS = os.getenv("EMAIL_PASS")     # App password for secure login

REDDIT_CLIENT_ID = os.getenv("REDDIT_CLIENT_ID")
REDDIT_SECRET = os.getenv("REDDIT_SECRET")
REDDIT_USER_AGENT = "autotrader_squeezer"

TICKERS = [
    "BEEM", "FSR", "EVGO", "PTEN", "BBBY", "RIOT",
    "AMC", "GME", "SPRT", "ATER", "PROG", "BBIG", "IRNT", "OPAD", "G<PERSON><PERSON>", "PHUN"
]

FINNHUB_BASE = "https://finnhub.io/api/v1/quote"


def fetch_price(ticker):
    url = f"{FINNHUB_BASE}?symbol={ticker}&token={API_KEY}"
    try:
        resp = requests.get(url)
        data = resp.json()
        return data.get("c")  # current price
    except Exception as e:
        print(f"Error fetching {ticker}: {e}")
        return None


def fetch_reddit_mentions():
    reddit = praw.Reddit(client_id=REDDIT_CLIENT_ID,
                         client_secret=REDDIT_SECRET,
                         user_agent=REDDIT_USER_AGENT)
    subs = ["wallstreetbets", "shortsqueeze"]
    mentions = Counter()

    for sub in subs:
        for post in reddit.subreddit(sub).hot(limit=50):
            text = f"{post.title} {post.selftext}".upper()
            for ticker in TICKERS:
                if f" {ticker} " in text or text.startswith(f"{ticker} "):
                    mentions[ticker] += 1

    return mentions


def generate_report():
    print("\n🤖 AutoTrader - Short Squeeze Scanner v2")
    print("=" * 50)
    print("🚨 SCANNING FOR SHORT-SQUEEZE CANDIDATES")

    reddit_mentions = fetch_reddit_mentions()
    results = []
    for t in TICKERS:
        price = fetch_price(t)
        if price and price <= 20:
            results.append({
                "Ticker": t,
                "Price": round(price, 2),
                "Mentions": reddit_mentions.get(t, 0)
            })

    df = pd.DataFrame(results)
    if not df.empty:
        df = df.sort_values(["Mentions", "Price"], ascending=[False, True])
        print(tabulate(df, headers="keys", tablefmt="fancy_grid"))
        save_report(df)
        send_email(df)
    else:
        print("No valid stocks under $20 found today.")


def save_report(df):
    filename = f"squeeze_candidates_{datetime.now().strftime('%Y%m%d_%H%M')}.csv"
    df.to_csv(filename, index=False)
    print(f"\n📁 Report saved as: {filename}")


def send_email(df):
    if not ALERT_EMAIL or not EMAIL_PASS:
        print("⚠️ Email alert skipped: missing ALERT_EMAIL or EMAIL_PASS")
        return

    msg = EmailMessage()
    msg['Subject'] = "📈 Squeeze Scanner Report"
    msg['From'] = ALERT_EMAIL
    msg['To'] = ALERT_EMAIL
    msg.set_content("Today's squeeze picks under $20:\n\n" + df.to_string(index=False))

    try:
        with SMTP_SSL('smtp.gmail.com', 465) as smtp:
            smtp.login(ALERT_EMAIL, EMAIL_PASS)
            smtp.send_message(msg)
        print("📬 Email alert sent.")
    except Exception as e:
        print(f"❌ Failed to send email: {e}")


if __name__ == "__main__":
    generate_report()
